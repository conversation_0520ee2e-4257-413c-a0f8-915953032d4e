---
description: Admin workflow for kost management system
---

1. <PERSON><PERSON> (default credentials)
   - email: <EMAIL>
   - password: admin
// turbo
2. Verification & Moderation
   - Verify new owners
   - Moderate property listings
   - Review documents
   - Handle complaints
   - Monitor suspicious activities

3. Statistics & Reports
   - View user analytics
   - Track booking trends
   - Monitor revenue
   - Generate reports
   - View system performance

4. Content Management
   - Manage listings
   - Handle content reports
   - Manage categories
   - Update terms & conditions
   - Manage banners

5. System Management
   - Manage users
   - Configure settings
   - Update system
   - Backup database
   - Monitor security

6. Support & Helpdesk
   - Handle user inquiries
   - Manage support tickets
   - Provide technical support
   - Update FAQ
   - Monitor feedback