---
description: User workflow for kost management system
---

1. User Registration/Login
// turbo
2. Search Kost
   - Enter location
   - Set price range
   - Add filters (amenities, room type)
   - View search results

3. View Kost Details
   - See room details
   - View amenities
   - Check location
   - View owner information

4. Make Booking
   - Select check-in/check-out dates
   - Fill personal information
   - Upload required documents
   - Submit booking request

5. Wait for Owner Review
   - Receive notification
   - Check booking status
   - View owner comments

6. Complete Payment
   - View payment details
   - Make payment
   - Upload payment proof
   - Wait for confirmation

7. Check-in Process
   - Receive check-in instructions
   - Complete final verification
   - Get room access

8. Profile Management
   - Update personal information
   - View booking history
   - Manage documents
   - Update payment methods

9. Notifications
   - Receive booking updates
   - Get payment reminders
   - View owner messages
   - Get system notifications
