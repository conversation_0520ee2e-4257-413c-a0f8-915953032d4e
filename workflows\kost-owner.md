---
description: Kost Owner workflow for kost management system
---

1. Owner Registration/Login
// turbo
2. Property Management
   - Add new property
   - Add rooms
   - Set pricing
   - Add amenities
   - Upload photos
   - Set location

3. Booking Management
   - View booking requests
   - Review documents
   - Approve/Reject bookings
   - Set check-in instructions
   - Manage booking calendar

4. Communication
   - Chat with tenants
   - Send notifications
   - Handle inquiries
   - Manage messages

5. Payment Management
   - Generate invoices
   - Track payments
   - Send payment reminders
   - View transaction history
   - Handle disputes

6. Property Maintenance
   - Schedule maintenance
   - Track repairs
   - Manage utilities
   - Update property status

7. Reports & Analytics
   - View occupancy rates
   - Track revenue
   - View tenant history
   - Generate reports
   - Monitor performance